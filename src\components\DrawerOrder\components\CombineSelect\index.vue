<template>
  <div class="flex flex-col gap-2">
    <n-select
      v-bind="$attrs"
      v-model:value="model"
      :options="options"
      :render-label="renderOption"
      :show-checkmark="false"
      ref="selectRef"
    />
    <template v-for="item in options" :key="item.value">
      <UploadFile
        v-if="model === item.value"
        v-model:fileList="appendix[item.value]"
        accept=".jpg,.jpeg,.png,.pdf"
        :max-size="5"
        :multiple="!!item.min && item.min !== 1"
        :max-count="item.max || 1"
      />
    </template>
  </div>

  <CurrentSelectTips v-if="notFinishOptions && notFinishOptions.length > 0">
    {{ notFinishOptions.join(',') }}
  </CurrentSelectTips>
</template>
<script setup lang="ts">
  import CurrentSelectTips from '@/components/DrawerOrder/components/CurrentSelectTips/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import { h, ref, computed } from 'vue';
  import { NSelect, type SelectOption } from 'naive-ui';
  const model = defineModel<string | number | undefined>('value', { required: true });
  interface SelectOptions extends SelectOption {
    finish?: boolean;
    required?: boolean;
    min?: number;
    max?: number;
  }
  const props = defineProps({
    options: {
      type: Array as PropType<SelectOptions[]>,
      default: () => [],
    },
  });
  //appendix 是以props.options value值为key的对象
  const optionsConst = [...props.options] as const;
  type OptionValue = (typeof optionsConst)[number]['value'];
  type Form = {
    [K in OptionValue]: any;
  };
  const appendix = defineModel<Form>('appendix', { required: true });
  function renderOption(option: SelectOptions) {
    const renderLabel = () => {
      if (!option.label) return option.label;
      if (!option.required) {
        return `${option.label}${option.finish ? '✔️' : ''}`;
      } else {
        return h('div', {}, [
          h('span', {}, { default: () => option.label }),
          h('span', { class: 'text-[#f00]' }, '*'),
          h('span', { class: 'finish-icon' }, option.finish ? '✔️' : ''),
        ]);
      }
    };
    return h(
      'div',
      {
        class: 'combine-select-option',
        onClick: () => {
          model.value = option.value;
        },
      },
      renderLabel()
    );
  }
  const selectRef = ref<InstanceType<typeof NSelect> | null>(null);
  const notFinishOptions = computed(() =>
    props.options.filter((item) => !item.finish).map((item) => item.label)
  );
  const focus = () => {
    selectRef.value?.focus();
  };
  const focusInput = () => {
    selectRef.value?.focusInput();
  };
  const blur = () => {
    selectRef.value?.blur();
  };
  const blurInput = () => {
    selectRef.value?.blurInput();
  };
  defineExpose({
    selectRef,
    focus,
    focusInput,
    blur,
    blurInput,
  });
</script>
<style lang="less" scoped></style>
