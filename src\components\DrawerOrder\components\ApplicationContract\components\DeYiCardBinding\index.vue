<template>
  <n-card>
    <!-- 德易绑卡 -->
    <n-collapse arrow-placement="right">
      <n-collapse-item>
        <template #header>
          <Title title="德易绑卡" :status="3" />
        </template>

        <div>
          <SubTitle title="德易绑卡" />

          <n-data-table :columns="cardBindingColumns" :data="cardBindingData" />
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';

  // 德易绑卡表格列定义
  const cardBindingColumns = [
    {
      title: '德易获取绑卡状态',
      key: 'bindingStatus',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            发起验证
          </n-button>
        );
      },
    },
  ];

  // 德易绑卡表格数据
  const cardBindingData = [
    {
      bindingStatus: '未绑卡',
    },
  ];
</script>

<style lang="less" scoped></style>
