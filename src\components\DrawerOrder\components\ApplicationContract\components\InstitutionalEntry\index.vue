<template>
  <n-card>
    <!-- 机构进件 -->
    <n-collapse arrow-placement="right">
      <n-collapse-item>
        <template #header>
          <Title title="机构进件" :status="3" />
        </template>

        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            size="medium"
            :model="formModel"
            :rules="formRules"
            label-width="160px"
          >
            <!-- 申请人居住信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item span="24">
                <n-form-item label="申请人居住地址" path="residentialAddress" required>
                  <AddressInput v-model="formModel.residentialAddress" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人有无驾照" path="hasDriverLicense" required>
                  <n-select
                    v-model:value="formModel.hasDriverLicense"
                    placeholder="请选择"
                    :options="yesNoOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人居住状况" path="residentialStatus" required>
                  <n-select
                    v-model:value="formModel.residentialStatus"
                    placeholder="请选择"
                    :options="residentialStatusOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人本地居住年限" path="localResidenceYears" required>
                  <n-select
                    v-model:value="formModel.localResidenceYears"
                    placeholder="请选择"
                    :options="residenceYearsOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人本地居住证明" path="localResidenceProof" required>
                  <n-select
                    v-model:value="formModel.localResidenceProof"
                    placeholder="请选择"
                    :options="proofTypeOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人学历情况" path="education" required>
                  <n-select
                    v-model:value="formModel.education"
                    placeholder="请选择"
                    :options="educationOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人就业状况" path="employmentStatus" required>
                  <n-select
                    v-model:value="formModel.employmentStatus"
                    placeholder="请选择"
                    :options="employmentStatusOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 申请人就业信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="申请人所属行业" path="industry" required>
                  <n-select
                    v-model:value="formModel.industry"
                    placeholder="请选择"
                    :options="industryOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人单位名称" path="companyName" required>
                  <n-input
                    v-model:value="formModel.companyName"
                    placeholder="请输入单位名称"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人单位性质" path="companyNature" required>
                  <n-select
                    v-model:value="formModel.companyNature"
                    placeholder="请选择"
                    :options="companyNatureOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item span="24">
                <n-form-item label="申请人单位地址" path="companyAddress" required>
                  <AddressInput v-model="formModel.companyAddress" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人工作职务" path="jobPosition" required>
                  <n-select
                    v-model:value="formModel.jobPosition"
                    placeholder="请选择"
                    :options="jobPositionOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人工作职称" path="jobTitle" required>
                  <n-select
                    v-model:value="formModel.jobTitle"
                    placeholder="请选择"
                    :options="jobTitleOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人税后月收入" path="monthlyIncome" required>
                  <n-input
                    v-model:value="formModel.monthlyIncome"
                    placeholder="请输入月收入"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人首次工作日期" path="firstWorkDate" required>
                  <n-date-picker
                    v-model:value="formModel.firstWorkDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="yyyy-MM-dd"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人现单位工作年限" path="currentWorkYears" required>
                  <n-input
                    v-model:value="formModel.currentWorkYears"
                    placeholder="工作年限默认一年"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请人现工作固话/手机" path="workPhone" required>
                  <n-input
                    v-model:value="formModel.workPhone"
                    placeholder="请输入联系电话"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="申请用途" path="applicationPurpose" required>
                  <n-input
                    v-model:value="formModel.applicationPurpose"
                    placeholder="请输入申请用途"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 公牌公司地址 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item span="24">
                <n-form-item label="公牌公司地址" path="publicPlateCompanyAddress">
                  <AddressInput v-model="formModel.publicPlateCompanyAddress" />
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                        <QuestionCircleTwotone />
                      </n-icon>
                    </template>
                    请输入详细地址
                  </n-tooltip>
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 婚姻状况 & 配偶信息 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="婚姻状况" path="maritalStatus" required>
                  <n-select
                    v-model:value="formModel.maritalStatus"
                    placeholder="请选择"
                    :options="maritalStatusOptions"
                    clearable
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="配偶姓名" path="spouseName">
                  <n-input
                    v-model:value="formModel.spouseName"
                    placeholder="请输入配偶姓名"
                    clearable
                  />
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                        <QuestionCircleTwotone />
                      </n-icon>
                    </template>
                    请输入配偶真实姓名
                  </n-tooltip>
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="配偶身份证号" path="spouseIdCard">
                  <n-input
                    v-model:value="formModel.spouseIdCard"
                    placeholder="请输入配偶身份证号"
                    maxlength="18"
                    clearable
                  />
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                        <QuestionCircleTwotone />
                      </n-icon>
                    </template>
                    请输入配偶身份证号码
                  </n-tooltip>
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="配偶手机号" path="spousePhone">
                  <n-input
                    v-model:value="formModel.spousePhone"
                    placeholder="请输入配偶手机号"
                    clearable
                  />
                  <n-tooltip trigger="hover">
                    <template #trigger>
                      <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                        <QuestionCircleTwotone />
                      </n-icon>
                    </template>
                    请输入配偶手机号码
                  </n-tooltip>
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <!-- 紧急联系人 -->
            <n-card class="my-4">
              <n-space vertical>
                <n-space
                  v-for="(item, index) in formModel.emergencyContacts"
                  :key="`emergencyContacts${index}`"
                  class="w-full"
                  justify="space-between"
                >
                  <n-form-item
                    :label="`紧急联系人${index + 1}与申请人关系`"
                    :path="`emergencyContacts[${index}].relationship`"
                    required
                    class="w-[300px]"
                  >
                    <n-select
                      v-model:value="item.relationship"
                      placeholder="请选择"
                      :options="relationshipOptions"
                      clearable
                    />
                    <n-tooltip v-if="index < 2" trigger="hover">
                      <template #trigger>
                        <n-icon size="18" class="ml-1 text-blue-600 cursor-pointer">
                          <QuestionCircleTwotone />
                        </n-icon>
                      </template>
                      联系人1,2不可选朋友
                    </n-tooltip>
                  </n-form-item>
                  <n-form-item
                    :label="`紧急联系人${index + 1}姓名`"
                    :path="`emergencyContacts[${index}].name`"
                    required
                  >
                    <n-input v-model:value="item.name" placeholder="请输入姓名" clearable />
                  </n-form-item>
                  <n-form-item
                    :label="`紧急联系人${index + 1}手机号`"
                    :path="`emergencyContacts[${index}].phone`"
                    required
                  >
                    <n-input v-model:value="item.phone" placeholder="请输入手机号" clearable />
                  </n-form-item>
                </n-space>
                <n-space justify="end">
                  <n-icon
                    class="cursor-pointer"
                    color="#1890ff"
                    @click="handleAddEmergencyContacts"
                  >
                    <PlusOutlined />
                  </n-icon>
                  <n-icon
                    class="cursor-pointer"
                    color="#1890ff"
                    @click="handleDeleteEmergencyContacts"
                  >
                    <MinusOutlined />
                  </n-icon>
                </n-space>
              </n-space>
            </n-card>

            <!-- 身份证信息 -->
            <n-card class="my-4">
              <n-flex align="center">
                <div>
                  <SubTitle title="身份证相关" />
                  <n-button type="info" @click="handlePreviewIdCard">预览</n-button>
                </div>
                <div class="flex-1 mb-[-25px]">
                  <n-grid :cols="GRID_COLS">
                    <n-grid-item>
                      <n-form-item label="申请人身份证起始日期" path="idCardStartDate" required>
                        <n-date-picker
                          v-model:value="formModel.idCardStartDate"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="yyyy-MM-dd"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="申请人身份证截止日期" path="idCardEndDate" required>
                        <n-date-picker
                          v-model:value="formModel.idCardEndDate"
                          type="date"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          placeholder="yyyy-MM-dd"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item>
                      <n-form-item label="申请人民族" path="ethnicity">
                        <n-select
                          v-model:value="formModel.ethnicity"
                          placeholder="请选择"
                          :options="ethnicityOptions"
                          clearable
                        />
                      </n-form-item>
                    </n-grid-item>
                    <n-grid-item span="24">
                      <n-form-item label="申请人户籍地址" path="householdAddress" required>
                        <AddressInput v-model="formModel.householdAddress" />
                      </n-form-item>
                    </n-grid-item>
                  </n-grid>
                </div>
              </n-flex>
            </n-card>

            <!-- 附件资料 -->
            <n-card class="my-4">
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="附件资料" path="attachments" required>
                    <n-flex direction="column">
                      <n-space>
                        <CombineSelect
                          class="w-[280px]"
                          v-model:value="selectValue"
                          :options="selectOptions"
                        />
                        <template v-for="item in selectOptions" :key="item.value">
                          <UploadFile
                            v-if="selectValue === item.value"
                            v-model:fileList="formModel.attachments[item.value]"
                            accept=".jpg,.jpeg,.png,.pdf"
                          />
                        </template>
                      </n-space>
                      <CurrentSelectTips>
                        {{ selectOptions.find((item) => item.value === selectValue)?.label }}
                      </CurrentSelectTips>
                    </n-flex>
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-card>

            <n-flex justify="center">
              <n-button type="info" size="large" @click="handleSubmit">确认提交</n-button>
            </n-flex>
          </n-form>

          <n-divider dashed />

          <SubTitle title="机构进件" desc="前置要求: 完善文件资料内容上传" />

          <n-data-table :columns="institutionalColumns" :data="institutionalData" />
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import AddressInput from '@/components/AddressInput/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import CurrentSelectTips from '@/components/DrawerOrder/components/CurrentSelectTips/index.vue';
  import {
    QuestionCircleTwotone,
    PlusOutlined,
    MinusOutlined,
    InfoCircleOutlined,
  } from '@vicons/antd';

  import { ref, reactive } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { computed } from 'vue';

  const formRef = ref<FormInst | null>(null);

  // 机构进件表格列定义
  const institutionalColumns = [
    {
      title: '签约机构',
      key: 'signingInstitution',
      align: 'center',
    },
    {
      title: '审批备注',
      key: 'approvalRemarks',
      align: 'center',
    },
    {
      title: '批复金额',
      key: 'approvedAmount',
      align: 'center',
    },
    {
      title: '批复期数',
      key: 'approvedTerms',
      align: 'center',
    },
    {
      title: '审批结果',
      key: 'approvalResult',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 机构进件表格数据
  const institutionalData = [
    {
      signingInstitution: '',
      approvalRemarks: '',
      approvedAmount: '',
      approvedTerms: '',
      approvalResult: '未提交',
    },
  ];

  const formModel = reactive({
    // 申请人居住信息
    residentialAddress: {
      province: '',
      city: '',
      district: '',
      detail: '',
    },
    hasDriverLicense: '有',
    residentialStatus: '自置',
    localResidenceYears: '',
    localResidenceProof: '',
    education: '',
    employmentStatus: '',
    // 申请人就业信息
    industry: '',
    companyName: '',
    companyNature: '其他',
    companyAddress: {
      province: '',
      city: '',
      district: '',
      detail: '',
    },
    jobPosition: '一般员工',
    jobTitle: '无',
    monthlyIncome: '',
    firstWorkDate: null,
    currentWorkYears: '1',
    workPhone: '13232323233',
    // 申请用途 & 公牌公司地址
    applicationPurpose: '',
    publicPlateCompanyAddress: {
      province: '',
      city: '',
      district: '',
      detail: '',
    },
    // 婚姻状况 & 配偶信息
    maritalStatus: '',
    spouseName: '',
    spouseIdCard: '',
    spousePhone: '',
    // 紧急联系人
    emergencyContacts: [
      {
        relationship: '',
        name: '',
        phone: '',
      },
    ], // { relationship: '', name: '', phone: '' }
    // 身份证信息
    idCardStartDate: null,
    idCardEndDate: null,
    ethnicity: '汉族',
    householdAddress: {
      province: '',
      city: '',
      district: '',
      detail: '',
    },
    // 附件资料
    attachments: {},
  });

  const formRules = {
    localResidenceProof: [{ required: true, message: '请填写本地居住证明', trigger: 'blur' }],
    education: [{ required: true, message: '请填写学历情况', trigger: 'blur' }],
    employmentStatus: [{ required: true, message: '请选择就业状况', trigger: 'change' }],
    industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
    companyName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
    companyNature: [{ required: true, message: '请选择单位性质', trigger: 'change' }],
    companyAddress: [{ required: true, message: '请填写单位地址', trigger: 'blur' }],
    jobPosition: [{ required: true, message: '请选择工作职务', trigger: 'change' }],
    jobTitle: [{ required: true, message: '请选择工作职称', trigger: 'change' }],
    monthlyIncome: [{ required: true, message: '请输入月收入', trigger: 'blur' }],
    firstWorkDate: [{ required: true, message: '请选择首次工作日期', trigger: 'change' }],
    currentWorkYears: [{ required: true, message: '请输入工作年限', trigger: 'blur' }],
    workPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    applicationPurpose: [{ required: true, message: '请输入申请用途', trigger: 'blur' }],
    maritalStatus: [{ required: true, message: '请选择婚姻状况', trigger: 'change' }],
    emergencyContacts: [{ required: true, message: '请选择关系', trigger: 'change' }],
    idCardStartDate: [{ required: true, message: '请选择身份证起始日期', trigger: 'change' }],
    idCardEndDate: [{ required: true, message: '请选择身份证截止日期', trigger: 'change' }],
    householdAddress: [{ required: true, message: '请填写户籍地址', trigger: 'blur' }],
    attachments: [{ required: true, message: '请选择附件资料', trigger: 'change' }],
  };

  // 下拉选项
  const yesNoOptions = [
    { label: '有', value: '有' },
    { label: '无', value: '无' },
  ];

  const residentialStatusOptions = [
    { label: '自置', value: '自置' },
    { label: '租赁', value: '租赁' },
    { label: '与父母同住', value: '与父母同住' },
    { label: '其他', value: '其他' },
  ];

  const residenceYearsOptions = [
    { label: '1年以下', value: '1年以下' },
    { label: '1-3年', value: '1-3年' },
    { label: '3-5年', value: '3-5年' },
    { label: '5年以上', value: '5年以上' },
  ];

  const proofTypeOptions = [
    { label: '房产证', value: '房产证' },
    { label: '租赁合同', value: '租赁合同' },
    { label: '居住证', value: '居住证' },
    { label: '其他', value: '其他' },
  ];

  const educationOptions = [
    { label: '小学', value: '小学' },
    { label: '初中', value: '初中' },
    { label: '高中', value: '高中' },
    { label: '大专', value: '大专' },
    { label: '本科', value: '本科' },
    { label: '硕士', value: '硕士' },
    { label: '博士', value: '博士' },
  ];

  const employmentStatusOptions = [
    { label: '在职', value: '在职' },
    { label: '离职', value: '离职' },
    { label: '自由职业', value: '自由职业' },
    { label: '学生', value: '学生' },
    { label: '退休', value: '退休' },
  ];

  const industryOptions = [
    { label: '制造业', value: '制造业' },
    { label: '服务业', value: '服务业' },
    { label: '金融业', value: '金融业' },
    { label: '教育', value: '教育' },
    { label: '医疗', value: '医疗' },
    { label: 'IT', value: 'IT' },
    { label: '其他', value: '其他' },
  ];

  const companyNatureOptions = [
    { label: '国有企业', value: '国有企业' },
    { label: '民营企业', value: '民营企业' },
    { label: '外资企业', value: '外资企业' },
    { label: '事业单位', value: '事业单位' },
    { label: '政府机关', value: '政府机关' },
    { label: '其他', value: '其他' },
  ];

  const jobPositionOptions = [
    { label: '一般员工', value: '一般员工' },
    { label: '主管', value: '主管' },
    { label: '经理', value: '经理' },
    { label: '总监', value: '总监' },
    { label: '总经理', value: '总经理' },
  ];

  const jobTitleOptions = [
    { label: '无', value: '无' },
    { label: '初级', value: '初级' },
    { label: '中级', value: '中级' },
    { label: '高级', value: '高级' },
    { label: '正高级', value: '正高级' },
  ];

  const maritalStatusOptions = [
    { label: '未婚', value: '未婚' },
    { label: '已婚', value: '已婚' },
    { label: '离异', value: '离异' },
    { label: '丧偶', value: '丧偶' },
  ];

  const relationshipOptions = [
    { label: '父母', value: '父母' },
    { label: '配偶', value: '配偶' },
    { label: '子女', value: '子女' },
    { label: '兄弟姐妹', value: '兄弟姐妹' },
    { label: '朋友', value: '朋友' },
    { label: '同事', value: '同事' },
    { label: '其他', value: '其他' },
  ];

  const ethnicityOptions = [
    { label: '汉族', value: '汉族' },
    { label: '蒙古族', value: '蒙古族' },
    { label: '回族', value: '回族' },
    { label: '藏族', value: '藏族' },
    { label: '维吾尔族', value: '维吾尔族' },
    { label: '其他', value: '其他' },
  ];

  const attachmentOptions = [
    { label: '居住稳定性凭证（1）', value: '居住稳定性凭证（1）' },
    { label: '行驶证', value: '行驶证' },
    { label: '驾驶证（1）', value: '驾驶证（1）' },
    { label: '中控台', value: '中控台' },
    { label: '里程表', value: '里程表' },
    { label: '原登记证（2）', value: '原登记证（2）' },
  ];

  const selectValue = ref('');
  const selectOptions = computed(() =>
    attachmentOptions.map((item) => ({
      label: item.label,
      value: item.value,
    }))
  );

  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交机构进件表单:', formModel);
      window.$message?.success('机构进件提交成功');
    });
  };

  const handlePreviewIdCard = () => {
    window.$message?.info('身份证预览功能');
  };

  const handleAddEmergencyContacts = () => {
    formModel.emergencyContacts.push({
      name: '',
      phone: '',
      relationship: '',
    });
  };

  const handleDeleteEmergencyContacts = (index: number) => {
    if (typeof index !== 'number') {
      index = formModel.emergencyContacts.length - 1;
    }

    formModel.emergencyContacts.splice(index, 1);
  };
</script>

<style lang="less" scoped></style>
