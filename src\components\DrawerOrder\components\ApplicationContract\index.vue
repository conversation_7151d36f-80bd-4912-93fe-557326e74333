<template>
  <n-space vertical>
    <!-- 车辆资料上传 -->
    <VehicleUpload />
    <!-- 机构进件 -->
    <InstitutionalEntry />
    <!-- 资方进件 -->
    <CapitalProviderEntry />
    <!-- 资方绑卡合同签署 -->
    <ContractSigning />
    <!-- 德易绑卡 -->
    <DeYiCardBinding />
    <!-- 德易签约 -->
    <DeYiSigning />
  </n-space>
</template>

<script setup lang="tsx">
  import VehicleUpload from './components/VehicleUpload/index.vue';
  import InstitutionalEntry from './components/InstitutionalEntry/index.vue';
  import CapitalProviderEntry from './components/CapitalProviderEntry/index.vue';
  import ContractSigning from './components/ContractSigning/index.vue';
  import DeYiCardBinding from './components/DeYiCardBinding/index.vue';
  import DeYiSigning from './components/DeYiSigning/index.vue';
</script>

<style lang="less" scoped></style>
