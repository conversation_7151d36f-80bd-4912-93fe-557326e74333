<template>
  <n-card>
    <!-- 资方进件 -->
    <n-collapse arrow-placement="right">
      <n-collapse-item>
        <template #header>
          <Title title="资方进件" :status="3" />
        </template>

        <div>
          <SubTitle title="资方进件" desc="前置要求：机构申请结果通过，跳过该签署阶段" />

          <!-- 审批结果表格 -->
          <n-data-table :columns="approvalColumns" :data="approvalData" class="mb-4" />

          <!-- 资方授权书签署表格 -->
          <n-data-table :columns="signingColumns" :data="signingData" />
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import { CopyOutlined } from '@vicons/antd';

  // 审批结果表格列定义
  const approvalColumns = [
    {
      title: '签约资方',
      key: 'signingCapitalProvider',
      align: 'center',
    },
    {
      title: '审批结果',
      key: 'approvalResult',
      align: 'center',
    },
    {
      title: '审批备注',
      key: 'approvalRemarks',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 审批结果表格数据
  const approvalData = [
    {
      signingCapitalProvider: '',
      approvalResult: '通过',
      approvalRemarks: '',
    },
  ];

  // 资方授权书签署表格列定义
  const signingColumns = [
    {
      title: '签约资方',
      key: 'signingCapitalProvider',
      align: 'center',
    },
    {
      title: '资方授权书签署地址',
      key: 'signingAddress',
      align: 'center',
      render(row: any) {
        return (
          <n-space align="center">
            <n-button text type="primary" onClick={() => handleCopyAddress(row.signingAddress)}>
              {row.signingAddress}
            </n-button>
            <n-button
              size="small"
              type="info"
              onClick={() => handleCopyAddress(row.signingAddress)}
            >
              <n-icon>
                <CopyOutlined />
              </n-icon>
              复制
            </n-button>
          </n-space>
        );
      },
    },
    {
      title: '签署状态',
      key: 'signingStatus',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 资方授权书签署表格数据
  const signingData = [
    {
      signingCapitalProvider: '',
      signingAddress: 'https://www.baidu.com/',
      signingStatus: '未签署',
    },
  ];

  // 复制地址功能
  const handleCopyAddress = (address: string) => {
    navigator.clipboard
      .writeText(address)
      .then(() => {
        window.$message?.success('地址已复制到剪贴板');
      })
      .catch(() => {
        window.$message?.error('复制失败');
      });
  };
</script>

<style lang="less" scoped></style>
