<template>
  <div>
    <n-drawer
      v-model:show="active"
      width="100%"
      :min-width="1000"
      placement="right"
      :mask-closable="false"
    >
      <n-drawer-content :native-scrollbar="false" closable>
        <Header @stage-click="handleStageClick" />
        <template v-if="currentStage">
          <PreTrial v-if="currentStage.id === 1" />
          <ApplicationContract v-else-if="currentStage.id === 2" />
          <FaceToFaceSigning v-else-if="currentStage.id === 3" />
          <GPSInstallation v-else-if="currentStage.id === 4" />
          <Mortgage v-else-if="currentStage.id === 5" />
          <LoanDisbursement v-else-if="currentStage.id === 6" />
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup lang="ts">
  import type { DrawerOrderProps, DrawerOrderEmits } from './types';
  import Header from '@/components/DrawerOrder/components/Header/index.vue';
  import PreTrial from '@/components/DrawerOrder/components/PreTrial/index.vue'; // 预审阶段
  import ApplicationContract from '@/components/DrawerOrder/components/ApplicationContract/index.vue'; // 进件签约
  import FaceToFaceSigning from '@/components/DrawerOrder/components/FaceToFaceSigning/index.vue'; // 面签阶段
  import GPSInstallation from '@/components/DrawerOrder/components/GPSInstallation/index.vue'; // GPS安装
  import Mortgage from '@/components/DrawerOrder/components/Mortgage/index.vue'; // 抵押阶段
  import LoanDisbursement from '@/components/DrawerOrder/components/LoanDisbursement/index.vue'; // 放款阶段
  import { onMounted, ref, provide } from 'vue';

  defineProps<DrawerOrderProps>();
  const emit = defineEmits<DrawerOrderEmits>();

  const active = ref(false);

  // 当前选中的阶段
  const currentStage = ref<any>(null);

  // 通过 provide 向所有子组件提供数据
  provide('currentStage', currentStage);

  onMounted(() => {
    active.value = true;
  });

  // 处理阶段点击事件
  const handleStageClick = (stage: any) => {
    console.log('父组件接收到阶段点击事件:', stage);
    currentStage.value = stage;
  };
</script>

<style lang="less" scoped></style>
