<template>
  <div class="w-full flex gap-[10px]">
    <n-select
      v-model:value="addressModel.province"
      placeholder="省份"
      :options="provinceOptions"
      clearable
      @update:value="handleProvinceChange"
      class="w-[120px]"
      children-field="c"
    />
    <n-select
      v-model:value="addressModel.city"
      placeholder="城市"
      :options="cityOptions"
      clearable
      @update:value="handleCityChange"
      :disabled="!addressModel.province"
      class="w-[120px]"
      children-field="c"
    />
    <n-select
      v-model:value="addressModel.district"
      placeholder="区县"
      :options="districtOptions"
      clearable
      :disabled="!addressModel.city"
      class="w-[120px]"
      children-field="c"
    />
    <n-input v-model:value="addressModel.detail" placeholder="详细地址" clearable style="flex: 1" />
  </div>
</template>

<script setup lang="ts">
  import { reactive, watch, ref, onMounted } from 'vue';
  import { getProvinceAndCityApi } from '@/api/global';

  interface AddressValue {
    province: string | number | null;
    city: string | number | null;
    district: string | number | null;
    detail: string;
  }

  interface Option {
    label: string;
    value: string | number;
    children?: Option[];
  }

  // 缓存配置
  const CACHE_KEY = 'address_province_city_data';
  const CACHE_EXPIRY = 15 * 60 * 1000; // 15分钟

  // 检查缓存是否有效
  const isValidCache = (cacheData: any): boolean => {
    if (!cacheData || !cacheData.timestamp) return false;
    const now = Date.now();
    return now - cacheData.timestamp < CACHE_EXPIRY;
  };

  // 获取缓存数据
  const getCacheData = (): Option[] | null => {
    try {
      const cacheStr = localStorage.getItem(CACHE_KEY);
      if (!cacheStr) return null;
      const cacheData = JSON.parse(cacheStr);
      return isValidCache(cacheData) ? cacheData.data : null;
    } catch (error) {
      console.error('读取缓存失败:', error);
      return null;
    }
  };

  // 设置缓存数据
  const setCacheData = (data: Option[]) => {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.error('设置缓存失败:', error);
    }
  };

  const props = defineProps<{
    modelValue?: AddressValue;
  }>();

  const emit = defineEmits<{
    'update:modelValue': [value: AddressValue];
  }>();

  const addressModel = reactive<AddressValue>({
    province: props.modelValue?.province || null,
    city: props.modelValue?.city || null,
    district: props.modelValue?.district || null,
    detail: props.modelValue?.detail || '',
  });

  const provinceOptions = ref<Option[]>([]);
  const cityOptions = ref<Option[]>([]);
  const districtOptions = ref<Option[]>([]);

  // 加载省份和城市数据
  const loadProvincesAndCities = async () => {
    // 先尝试从缓存获取
    const cachedData = getCacheData();
    if (cachedData) {
      provinceOptions.value = cachedData;
      return;
    }

    // 缓存不存在或已过期，从API获取
    try {
      const { data } = await getProvinceAndCityApi();
      const transformedData = data.map((province: any) => ({
        label: province.provinceName,
        value: province.provinceCode,
        children: province.cityDtoList?.map((city: any) => ({
          label: city.fullName,
          value: city.id,
        })),
      }));

      // 保存到缓存
      provinceOptions.value = transformedData;
      setCacheData(transformedData);
    } catch (error) {
      console.error('加载省份和城市数据失败:', error);
    }
  };

  // 处理省份变化
  const handleProvinceChange = (value: string | number | null) => {
    // 清空城市和区县
    addressModel.city = null;
    addressModel.district = null;
    cityOptions.value = [];
    districtOptions.value = [];

    if (value) {
      // 根据选择的省份加载城市数据
      const selectedProvince = provinceOptions.value.find((p) => p.value === value);
      if (selectedProvince?.children) {
        cityOptions.value = selectedProvince.children.map((city) => ({
          label: city.label,
          value: city.value,
        }));
      }
    }
  };

  // 处理城市变化
  const handleCityChange = () => {
    // 清空区县
    addressModel.district = null;
    districtOptions.value = [];

    if (addressModel.city) {
      // 根据选择的城市加载区县数据
      const selectedCity = cityOptions.value.find((c) => c.value === addressModel.city);
      if (selectedCity?.children) {
        districtOptions.value = selectedCity.children.map((district) => ({
          label: district.label,
          value: district.value,
        }));
      }
    }
  };

  // 监听内部值变化，同步到外部
  watch(
    () => addressModel,
    (newValue) => {
      emit('update:modelValue', { ...newValue });
    },
    { deep: true }
  );

  // 监听外部值变化，同步到内部
  watch(
    () => props.modelValue,
    (newValue) => {
      if (newValue) {
        addressModel.province = newValue.province || null;
        addressModel.city = newValue.city || null;
        addressModel.district = newValue.district || null;
        addressModel.detail = newValue.detail || '';
      }
    },
    { deep: true }
  );

  onMounted(() => {
    loadProvincesAndCities();
  });
</script>

<style lang="less" scoped></style>
