<template>
  <n-card>
    <!-- 资方绑卡合同签署 -->
    <n-collapse arrow-placement="right">
      <n-collapse-item>
        <template #header>
          <Title title="资方绑卡合同签署" :status="3" />
        </template>

        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            size="medium"
            :model="formModel"
            :rules="formRules"
            label-width="160px"
          >
            <!-- 附件资料 -->
            <n-grid :cols="GRID_COLS">
              <n-grid-item>
                <n-form-item label="附件资料" path="attachments" required>
                  <n-flex direction="column">
                    <n-space>
                      <CombineSelect
                        class="w-[280px]"
                        v-model:value="selectValue"
                        :options="selectOptions"
                      />
                      <template v-for="item in selectOptions" :key="item.value">
                        <UploadFile
                          v-if="selectValue === item.value"
                          v-model:fileList="formModel.attachments[item.value]"
                          accept=".jpg,.jpeg,.png,.pdf"
                        />
                      </template>
                    </n-space>
                    <CurrentSelectTips>
                      {{ selectOptions.find((item) => item.value === selectValue)?.label }}
                    </CurrentSelectTips>
                  </n-flex>
                </n-form-item>
              </n-grid-item>
            </n-grid>

            <n-flex justify="center">
              <n-button type="info" size="large" @click="handleSubmit">确认提交</n-button>
            </n-flex>
          </n-form>

          <n-divider dashed />

          <SubTitle title="资方绑卡合同签署" desc="前置要求:完善文件资料内容上传" />

          <n-data-table :columns="contractColumns" :data="contractData" />
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import CurrentSelectTips from '@/components/DrawerOrder/components/CurrentSelectTips/index.vue';
  import { PlusOutlined, InfoCircleOutlined, QuestionCircleTwotone } from '@vicons/antd';

  import { ref, reactive } from 'vue';
  import type { FormInst, UploadFileInfo } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';
  import { computed } from 'vue';

  const formRef = ref<FormInst | null>(null);
  const fileList = ref<UploadFileInfo[]>([]);

  const formModel = reactive({
    attachments: '人车合影',
  });

  const formRules = {
    attachments: [{ required: true, message: '请选择附件资料', trigger: 'change' }],
  };

  // 附件资料选项
  const attachmentOptions = [
    { label: '人车合影', value: '人车合影' },
    { label: '个人信息页', value: '个人信息页' },
    { label: '连带信息页', value: '连带信息页' },
    { label: 'GPS安装合同', value: 'GPS安装合同' },
    { label: '车身左前方45°', value: '车身左前方45°' },
    { label: '车身右后方45°', value: '车身右后方45°' },
    { label: '发动机全景', value: '发动机全景' },
    { label: '挂靠协议-三方', value: '挂靠协议-三方' },
    { label: '客户&挂靠公司', value: '客户&挂靠公司' },
    { label: '股东会议', value: '股东会议' },
    { label: '抵押合同-公牌1', value: '抵押合同-公牌1' },
    { label: '抵押合同-公牌2', value: '抵押合同-公牌2' },
    { label: '挂靠协议1', value: '挂靠协议1' },
    { label: '挂靠协议2', value: '挂靠协议2' },
  ];

  const selectValue = ref('');
  const selectOptions = computed(() =>
    attachmentOptions.map((item) => ({
      label: item.label,
      value: item.value,
    }))
  );

  // 合同签署表格列定义
  const contractColumns = [
    {
      title: '资方绑卡签约合同签署地址',
      key: 'signingAddress',
      align: 'center',
      render(row: any) {
        return (
          <n-space align="center">
            <n-button text type="info" onClick={() => handleCopyAddress(row.signingAddress)}>
              {row.signingAddress}
            </n-button>
            <n-button
              size="small"
              type="info"
              onClick={() => handleCopyAddress(row.signingAddress)}
            >
              复制
            </n-button>
          </n-space>
        );
      },
    },
    {
      title: '签署结果',
      key: 'signingResult',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 合同签署表格数据
  const contractData = [
    {
      signingAddress: 'https://www.baidu.com/',
      signingResult: '待提交',
    },
  ];

  // 复制地址功能
  const handleCopyAddress = (address: string) => {
    navigator.clipboard
      .writeText(address)
      .then(() => {
        window.$message?.success('地址已复制到剪贴板');
      })
      .catch(() => {
        window.$message?.error('复制失败');
      });
  };

  // 文件上传处理
  const handleFileChange = (options: { fileList: UploadFileInfo[] }) => {
    fileList.value = options.fileList;
  };

  // 表单提交
  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交合同签署表单:', formModel);
      window.$message?.success('合同签署提交成功');
    });
  };
</script>

<style lang="less" scoped></style>
