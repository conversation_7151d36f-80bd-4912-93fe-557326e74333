<template>
  <n-space vertical>
    <!-- GPS安装要求 -->
    <n-card>
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="GPS安装要求" :status="2" />
          </template>

          <n-data-table
            :columns="[
              {
                title: '查询结果',
                key: 'status',
                align: 'center',
              },
            ]"
            :data="[{ status: '成功' }]"
          />
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <!-- GPS安装 -->
    <n-card>
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="GPS安装" :status="2" />
          </template>

          <div>
            <!-- 安装区域地址 + 提交 -->
            <div class="flex items-center gap-2 mb-2">
              <span>安装区域地址：</span>
              <AddressInput v-model="installAddress" />
              <n-button type="info" @click="handleSubmitAddress">确认提交</n-button>
            </div>

            <!-- 安装要求 -->
            <n-card class="my-2">
              <SubTitle title="GPS安装要求" />
              <n-data-table :columns="requireColumns" :data="requireData" />

              <div class="grid grid-cols-2 gap-4 mt-4 w-[400px]">
                <n-input-group>
                  <n-input-group-label>有线GPS数</n-input-group-label>
                  <n-input v-model:value="wiredCount" disabled />
                </n-input-group>
                <n-input-group>
                  <n-input-group-label>无线GPS数</n-input-group-label>
                  <n-input v-model:value="wirelessCount" disabled />
                </n-input-group>
              </div>
            </n-card>

            <!-- GPS安装进度 -->
            <n-card class="my-2">
              <SubTitle title="GPS安装" />
              <n-data-table :columns="progressColumns" :data="progressData" />
            </n-card>
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';
  import AddressInput from '@/components/AddressInput/index.vue';
  import { ref } from 'vue';

  type Row = Record<string, any>;

  const installAddress = ref({ province: null, city: null, district: null, detail: '' });
  const wiredCount = ref(2);
  const wirelessCount = ref(1);

  const requireColumns = [
    { title: 'GPS序号', key: 'idx', align: 'center' },
    { title: 'GPS类型', key: 'type', align: 'center' },
    { title: 'GPS编号', key: 'code', align: 'center' },
    { title: '安装方式', key: 'installType', align: 'center' },
    { title: 'GPS供应商列表', key: 'vendor', align: 'center' },
  ];

  const requireData: Row[] = [
    { idx: 1, type: '有线', code: '132132', installType: '自行安装', vendor: 'AAA' },
    { idx: 2, type: '有线', code: '132132', installType: '带签安装', vendor: 'BBB' },
    { idx: 3, type: '无线', code: '132132', installType: '自行安装', vendor: 'CCC' },
  ];

  const progressColumns = [
    { title: 'GPS设备', key: 'device', align: 'center' },
    { title: '安装进度查询', key: 'status', align: 'center' },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  const progressData: Row[] = [{ device: '', status: '待提交' }];

  const handleSubmitAddress = () => {
    window.$message?.success('地址提交成功');
  };
</script>

<style lang="less" scoped></style>
