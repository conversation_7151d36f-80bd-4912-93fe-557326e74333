<template>
  <n-space vertical>
    <n-card>
      <!-- 面签阶段 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="面签阶段" :status="2" />
          </template>

          <div>
            <SubTitle title="面签阶段" />

            <n-data-table :columns="signingColumns" :data="signingData" />
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';

  // 面签阶段表格列定义
  const signingColumns = [
    {
      title: '远程面签地址',
      key: 'signingAddress',
      align: 'center',
      render(row: any) {
        return (
          <n-space align="center">
            <n-button text type="info" onClick={() => handleCopyAddress(row.signingAddress)}>
              {row.signingAddress}
            </n-button>
            <n-button
              size="small"
              type="info"
              onClick={() => handleCopyAddress(row.signingAddress)}
            >
              复制
            </n-button>
          </n-space>
        );
      },
    },
    {
      title: '签署结果',
      key: 'signingResult',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 面签阶段表格数据
  const signingData = [
    {
      signingAddress: 'https://www.baidu.com/',
      signingResult: '开始面签',
    },
  ];

  // 复制地址功能
  const handleCopyAddress = (address: string) => {
    navigator.clipboard
      .writeText(address)
      .then(() => {
        window.$message?.success('地址已复制到剪贴板');
      })
      .catch(() => {
        window.$message?.error('复制失败');
      });
  };
</script>

<style lang="less" scoped></style>
