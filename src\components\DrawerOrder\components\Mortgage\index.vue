<template>
  <n-space vertical>
    <n-card>
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="GPS、保险、抵押审批" :status="2" />
          </template>

          <div>
            <n-form
              ref="formRef"
              label-placement="left"
              size="medium"
              :model="formModel"
              :rules="formRules"
              label-width="120px"
            >
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="保险公司" :required="!autoSave" path="insuranceCompany">
                    <n-input
                      v-model:value="formModel.insuranceCompany"
                      placeholder="请输入保险公司"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item
                    label="商业险到期日期"
                    :required="!autoSave"
                    path="commercialInsuranceExpirationDate"
                  >
                    <n-date-picker
                      v-model:value="formModel.commercialInsuranceExpirationDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="yyyy-MM-dd"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="保险渠道" :required="!autoSave" path="insuranceChannel">
                    <n-select
                      v-model:value="formModel.insuranceChannel"
                      placeholder="请选择保险渠道"
                      :options="insuranceChannelOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-grid :cols="GRID_COLS">
                <n-grid-item>
                  <n-form-item label="抵押日期" :required="!autoSave" path="mortgageDate">
                    <n-date-picker
                      v-model:value="formModel.mortgageDate"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      placeholder="yyyy-MM-dd"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="抵押材料" :required="!autoSave" path="mortgageMaterials">
                    <n-select
                      v-model:value="formModel.mortgageMaterials"
                      placeholder="请选择抵押材料"
                      :options="mortgageMaterialsOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-card>
                <n-space align="start" :vertical="false">
                  <div>
                    <p>车辆登记相关</p>
                    <n-button size="tiny" type="primary">预览</n-button>
                  </div>
                  <n-form-item label="发证机关" :required="!autoSave" path="issuingAuthority">
                    <n-input
                      v-model:value="formModel.issuingAuthority"
                      placeholder="请输入发证机关"
                    />
                  </n-form-item>
                </n-space>
              </n-card>
              <n-card class="mt-2">
                <n-space align="start" :vertical="false">
                  <div>
                    <p> 车辆登记证相关</p>
                    <n-button size="tiny" type="primary">预览</n-button>
                  </div>
                  <n-form-item label="证书编号" :required="!autoSave" path="certificateNumber">
                    <n-input
                      v-model:value="formModel.certificateNumber"
                      placeholder="请输入证书编号"
                    />
                  </n-form-item>
                </n-space>
              </n-card>
              <n-card class="mt-2">
                <n-form-item label="附件资料" :required="!autoSave" path="appendix">
                  <n-space align="start" :vertical="false">
                    <CombineSelect
                      class="w-[280px]"
                      v-model:value="selectValue"
                      :options="selectOptions"
                    />
                    <template v-for="item in selectOptions" :key="item.value">
                      <UploadFile
                        v-if="selectValue === item.value"
                        v-model:fileList="formModel.appendix[item.value]"
                        accept=".jpg,.jpeg,.png,.pdf"
                        :max-size="5"
                        :multiple="!!item.min && item.min !== 1"
                        :max-count="item.max || 1"
                      />
                    </template>
                  </n-space>
                </n-form-item>
              </n-card>
              <n-flex justify="center" class="mt-2">
                <n-button type="info" size="large" @click="handleSubmit"> 确认提交 </n-button>
              </n-flex>
            </n-form>
            <n-divider dashed />

            <SubTitle title="GPS、保险、抵押审批" />

            <n-data-table :columns="columns" :data="data" />
          </div>
        </n-collapse-item>
      </n-collapse>
    </n-card>

    <n-card>
      <!-- 授权书签署 -->
      <n-collapse arrow-placement="right">
        <n-collapse-item>
          <template #header>
            <Title title="警邮回执" :status="3" />
          </template>
          <n-card>
            <n-form
              ref="formRef2"
              label-placement="left"
              size="medium"
              :model="formModel2"
              :rules="formRules2"
              label-width="120px"
            >
              <n-form-item label="附件资料" :required="!autoSave" path="appendix">
                <n-space align="start" :vertical="false">
                  <CombineSelect
                    class="w-[280px]"
                    v-model:value="selectValue2"
                    :options="selectOptions2"
                    v-model:appendix="formModel2.appendix"
                  />
                  <!-- <template v-for="item in selectOptions2" :key="item.value">
                    <UploadFile
                      v-if="selectValue2 === item.value"
                      v-model:fileList="formModel2.appendix[item.value]"
                      accept=".jpg,.jpeg,.png,.pdf"
                      :max-size="5"
                      :multiple="!!item.min && item.min !== 1"
                      :max-count="item.max || 1"
                    />
                  </template> -->
                </n-space>
              </n-form-item>
            </n-form>
          </n-card>

          <n-flex justify="center" class="mt-2">
            <n-button type="info" size="large" @click="handleSubmit2"> 确认提交 </n-button>
          </n-flex>
          <n-divider dashed />
          <SubTitle
            title="警邮回执"
            desc="前置要求：抵押材料为警邮回执时可先放后抵，并后续补充抵押相关登记证信息"
          />
          <n-data-table :columns="columns2" :data="data2" />
        </n-collapse-item>
      </n-collapse>
    </n-card>
  </n-space>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import UploadFile from '@/components/UploadFile/index.vue';
  import CombineSelect from '@/components/DrawerOrder/components/CombineSelect/index.vue';
  import { ref, reactive, computed } from 'vue';
  import type { FormInst } from 'naive-ui';
  import { GRID_COLS } from '@/components/DrawerOrder/config';

  const formRef = ref<FormInst | null>(null);
  const formRef2 = ref<FormInst | null>(null);
  const defaultFormModel = {
    insuranceCompany: '', //保险公司
    commercialInsuranceExpirationDate: null, //商业险到期日期
    insuranceChannel: null, //保险渠道
    mortgageDate: null, //抵押日期
    mortgageMaterials: null, //抵押材料
    issuingAuthority: '', //发证机关
    certificateNumber: '', //证书编号
    appendix: {
      //附件资料
      mortgageCertificate: [], //抵押信息登记证
      commercialInsurancePolicy: '', //商业保单
      vehicleStatus: '', //机动车状态（12123截图)
      policeMail: '', //警邮回执
    },
  };
  const defaultFormModel2 = {
    appendix: {
      mortgageCertificate: [], //抵押信息登记证
    },
  };
  const formModel = reactive({ ...defaultFormModel });
  const formModel2 = reactive({ ...defaultFormModel2 });
  const autoSave = ref(false);
  const insuranceChannelOptions = [];
  const mortgageMaterialsOptions = ref([]);
  const selectValue = ref('');
  const selectOptions = computed(() => [
    {
      value: 'mortgageCertificate',
      label: `抵押信息登记证(2)`,
      finish: formModel.appendix.mortgageCertificate?.length === 2,
      required: true,
      min: 2,
      max: 9,
    },
    {
      value: 'commercialInsurancePolicy',
      label: `商业保单`,
      finish: !!formModel.appendix.commercialInsurancePolicy,
      required: true,
    },
    {
      value: 'vehicleStatus',
      label: `机动车状态（12123截图） `,
      finish: !!formModel.appendix.vehicleStatus,
      required: true,
    },
    {
      value: 'policeMail',
      label: `警邮回执`,
      finish: !!formModel.appendix.policeMail,
      required: true,
    },
  ]);
  const selectValue2 = ref('');
  const selectOptions2 = computed(() => [
    {
      value: 'mortgageCertificate',
      label: `抵押信息登记证(2)`,
      finish: formModel2.appendix.mortgageCertificate?.length === 2,
      required: true,
      min: 2,
      max: 9,
    },
  ]);
  const formRules = {
    appendix: [
      {
        validator: (_rule, value) => {
          for (const item of selectOptions.value) {
            if (!item.finish) {
              return new Error(`请完善${item.label}`);
            }
          }
          return true;
        },
        trigger: 'change',
      },
    ],
  };
  const formRules2 = {
    appendix: [
      {
        validator: (_rule, value) => {
          for (const item of selectOptions2.value) {
            if (!item.finish) {
              return new Error(`请完善${item.label}`);
            }
          }
          return true;
        },
        trigger: 'change',
      },
    ],
  };

  const searchLoading1 = ref(false);
  const columns = [
    {
      title: '审批结果查询',
      key: 'status',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" disabled={searchLoading1.value} type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data = [
    {
      key: '1',
      status: '待提交',
    },
  ];
  const searchLoading2 = ref(false);
  const columns2 = [
    {
      title: '审批结果',
      key: 'status',
      align: 'center',
    },
    {
      title: '审批备注',
      key: 'remark',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" disabled={searchLoading2.value} type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];
  const data2 = [];
  const handleSubmit = async () => {
    await formRef.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel);
      window.$message?.success('提交成功');
    });
  };
  const handleSubmit2 = async () => {
    await formRef2.value?.validate((errors) => {
      if (errors) {
        console.error('表单验证失败:', errors);
        return;
      }
      console.log('提交表单:', formModel);
      window.$message?.success('提交成功');
    });
  };
</script>

<style lang="less" scoped></style>
