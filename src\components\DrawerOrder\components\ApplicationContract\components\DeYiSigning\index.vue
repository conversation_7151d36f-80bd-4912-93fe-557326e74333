<template>
  <n-card>
    <!-- 德易签约 -->
    <n-collapse arrow-placement="right">
      <n-collapse-item>
        <template #header>
          <Title title="德易签约" :status="3" />
        </template>

        <div>
          <SubTitle title="德易签约" />

          <n-data-table :columns="signingColumns" :data="signingData" />
        </div>
      </n-collapse-item>
    </n-collapse>
  </n-card>
</template>

<script setup lang="tsx">
  import Title from '@/components/DrawerOrder/components/Title/index.vue';
  import SubTitle from '@/components/DrawerOrder/components/SubTitle/index.vue';

  // 德易签约表格列定义
  const signingColumns = [
    {
      title: '德易绑卡签约合同签署地址',
      key: 'signingAddress',
      align: 'center',
      render(row: any) {
        return (
          <n-space align="center">
            <n-button text type="info" onClick={() => handleCopyAddress(row.signingAddress)}>
              {row.signingAddress}
            </n-button>
            <n-button
              size="small"
              type="info"
              onClick={() => handleCopyAddress(row.signingAddress)}
            >
              复制
            </n-button>
          </n-space>
        );
      },
    },
    {
      title: '签署结果',
      key: 'signingResult',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render() {
        return (
          <n-button size="small" type="info">
            状态查询
          </n-button>
        );
      },
    },
  ];

  // 德易签约表格数据
  const signingData = [
    {
      signingAddress: 'https://www.baidu.com/',
      signingResult: '待提交',
    },
  ];

  // 复制地址功能
  const handleCopyAddress = (address: string) => {
    navigator.clipboard
      .writeText(address)
      .then(() => {
        window.$message?.success('地址已复制到剪贴板');
      })
      .catch(() => {
        window.$message?.error('复制失败');
      });
  };
</script>

<style lang="less" scoped></style>
